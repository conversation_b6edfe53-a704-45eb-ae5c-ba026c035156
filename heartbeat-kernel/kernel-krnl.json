{"name": "heartbeat.kernel", "version": "1.0.0", "address": "0x7Fb51Ab5a320A541B7f919eFAe5db46B2ebBC44b", "description": "A KRNL-integrated kernel that returns health status and current block timestamp for on-chain monitoring", "network": "sepolia", "chainId": "11155111", "tokenAuthorityAddress": "0xC959e1A9c3dE5949A0Ec1d5Df9555C27e2357257", "tokenAuthorityPublicKey": "0x493f395c80C178Ae32Ef3b88325739E80073118A", "abi": [{"type": "constructor", "stateMutability": "undefined", "payable": false, "inputs": [{"type": "address", "name": "_tokenAuthorityPublicKey"}]}, {"type": "error", "name": "ECDSAInvalidSignature", "inputs": []}, {"type": "error", "name": "ECDSAInvalidSignatureLength", "inputs": [{"type": "uint256", "name": "length"}]}, {"type": "error", "name": "ECDSAInvalidSignatureS", "inputs": [{"type": "bytes32", "name": "s"}]}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"type": "address", "name": "owner"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"type": "address", "name": "account"}]}, {"type": "error", "name": "UnauthorizedTransaction", "inputs": []}, {"type": "event", "anonymous": false, "name": "HeartbeatChecked", "inputs": [{"type": "address", "name": "caller", "indexed": true}, {"type": "uint256", "name": "timestamp", "indexed": false}, {"type": "string", "name": "status", "indexed": false}, {"type": "uint256", "name": "kernelScore", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "KernelResponseReceived", "inputs": [{"type": "address", "name": "caller", "indexed": true}, {"type": "uint256", "name": "kernelId", "indexed": false}, {"type": "bytes", "name": "result", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "OwnershipTransferred", "inputs": [{"type": "address", "name": "previousOwner", "indexed": true}, {"type": "address", "name": "new<PERSON>wner", "indexed": true}]}, {"type": "function", "name": "deployedAt", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint256", "name": ""}]}, {"type": "function", "name": "description", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}, {"type": "function", "name": "execute", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": "status"}, {"type": "uint256", "name": "timestamp"}]}, {"type": "function", "name": "executeExtended", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": "status"}, {"type": "uint256", "name": "timestamp"}, {"type": "uint256", "name": "blockNumber"}, {"type": "uint256", "name": "uptime"}]}, {"type": "function", "name": "executed", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes", "name": ""}], "outputs": [{"type": "bool", "name": ""}]}, {"type": "function", "name": "getDeploymentInfo", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "uint256", "name": "deploymentTimestamp"}, {"type": "uint256", "name": "currentUptime"}]}, {"type": "function", "name": "getHeartbeatData", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "tuple", "name": "data", "components": [{"type": "string", "name": "status"}, {"type": "uint256", "name": "timestamp"}, {"type": "uint256", "name": "blockNumber"}, {"type": "uint256", "name": "uptime"}, {"type": "uint256", "name": "kernelScore"}]}]}, {"type": "function", "name": "getMetadata", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": "contractName"}, {"type": "string", "name": "contractVersion"}, {"type": "string", "name": "contractDescription"}]}, {"type": "function", "name": "is<PERSON><PERSON><PERSON>", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "bool", "name": "healthy"}]}, {"type": "function", "name": "name", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}, {"type": "function", "name": "owner", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "protectedHeartbeat", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "krnlPayload", "components": [{"type": "bytes", "name": "auth"}, {"type": "bytes", "name": "kernelResponses"}, {"type": "bytes", "name": "kernelParams"}]}, {"type": "string", "name": "input"}], "outputs": []}, {"type": "function", "name": "renounceOwnership", "constant": false, "payable": false, "inputs": [], "outputs": []}, {"type": "function", "name": "setTokenAuthorityPublicKey", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "_tokenAuthorityPublicKey"}], "outputs": []}, {"type": "function", "name": "tokenAuthorityPublicKey", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "transferOwnership", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "new<PERSON>wner"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "functions": {"execute": {"signature": "execute()", "returns": ["string", "uint256"], "description": "Returns status 'OK' and current block timestamp", "protected": false}, "protectedHeartbeat": {"signature": "protectedHeartbeat((bytes,bytes,bytes),string)", "returns": [], "description": "KRNL-protected heartbeat function with kernel integration", "protected": true}, "executeExtended": {"signature": "executeExtended()", "returns": ["string", "uint256", "uint256", "uint256"], "description": "Returns status, timestamp, block number, and uptime", "protected": false}, "isHealthy": {"signature": "isHealthy()", "returns": ["bool"], "description": "Returns true if the contract is healthy", "protected": false}}, "deploymentInfo": {"deployer": "0xf672e2b5b3072b7eE79e3BDdcF5907032c8D0C74", "transactionHash": "0x27903628c01a355c090e36fe8fdb92397897d5d74982b7093b06690ef0d65081", "blockNumber": null, "timestamp": "2025-07-07T12:48:50.258Z"}}