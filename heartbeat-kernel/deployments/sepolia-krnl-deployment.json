{"contractName": "HeartbeatKernelKRNL", "contractAddress": "0x7Fb51Ab5a320A541B7f919eFAe5db46B2ebBC44b", "deployerAddress": "0xf672e2b5b3072b7eE79e3BDdcF5907032c8D0C74", "network": "sepolia", "chainId": "11155111", "transactionHash": "0x27903628c01a355c090e36fe8fdb92397897d5d74982b7093b06690ef0d65081", "blockNumber": null, "gasUsed": "1170666", "timestamp": "2025-07-07T12:48:50.256Z", "tokenAuthorityPublicKey": "0x493f395c80C178Ae32Ef3b88325739E80073118A", "tokenAuthorityContract": "0xC959e1A9c3dE5949A0Ec1d5Df9555C27e2357257", "abi": "[{\"type\":\"constructor\",\"stateMutability\":\"undefined\",\"payable\":false,\"inputs\":[{\"type\":\"address\",\"name\":\"_tokenAuthorityPublicKey\"}]},{\"type\":\"error\",\"name\":\"ECDSAInvalidSignature\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"ECDSAInvalidSignatureLength\",\"inputs\":[{\"type\":\"uint256\",\"name\":\"length\"}]},{\"type\":\"error\",\"name\":\"ECDSAInvalidSignatureS\",\"inputs\":[{\"type\":\"bytes32\",\"name\":\"s\"}]},{\"type\":\"error\",\"name\":\"OwnableInvalidOwner\",\"inputs\":[{\"type\":\"address\",\"name\":\"owner\"}]},{\"type\":\"error\",\"name\":\"OwnableUnauthorizedAccount\",\"inputs\":[{\"type\":\"address\",\"name\":\"account\"}]},{\"type\":\"error\",\"name\":\"UnauthorizedTransaction\",\"inputs\":[]},{\"type\":\"event\",\"anonymous\":false,\"name\":\"HeartbeatChecked\",\"inputs\":[{\"type\":\"address\",\"name\":\"caller\",\"indexed\":true},{\"type\":\"uint256\",\"name\":\"timestamp\",\"indexed\":false},{\"type\":\"string\",\"name\":\"status\",\"indexed\":false},{\"type\":\"uint256\",\"name\":\"kernelScore\",\"indexed\":false}]},{\"type\":\"event\",\"anonymous\":false,\"name\":\"KernelResponseReceived\",\"inputs\":[{\"type\":\"address\",\"name\":\"caller\",\"indexed\":true},{\"type\":\"uint256\",\"name\":\"kernelId\",\"indexed\":false},{\"type\":\"bytes\",\"name\":\"result\",\"indexed\":false}]},{\"type\":\"event\",\"anonymous\":false,\"name\":\"OwnershipTransferred\",\"inputs\":[{\"type\":\"address\",\"name\":\"previousOwner\",\"indexed\":true},{\"type\":\"address\",\"name\":\"newOwner\",\"indexed\":true}]},{\"type\":\"function\",\"name\":\"deployedAt\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"uint256\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"description\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"string\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"execute\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"string\",\"name\":\"status\"},{\"type\":\"uint256\",\"name\":\"timestamp\"}]},{\"type\":\"function\",\"name\":\"executeExtended\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"string\",\"name\":\"status\"},{\"type\":\"uint256\",\"name\":\"timestamp\"},{\"type\":\"uint256\",\"name\":\"blockNumber\"},{\"type\":\"uint256\",\"name\":\"uptime\"}]},{\"type\":\"function\",\"name\":\"executed\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[{\"type\":\"bytes\",\"name\":\"\"}],\"outputs\":[{\"type\":\"bool\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"getDeploymentInfo\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"uint256\",\"name\":\"deploymentTimestamp\"},{\"type\":\"uint256\",\"name\":\"currentUptime\"}]},{\"type\":\"function\",\"name\":\"getHeartbeatData\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"tuple\",\"name\":\"data\",\"components\":[{\"type\":\"string\",\"name\":\"status\"},{\"type\":\"uint256\",\"name\":\"timestamp\"},{\"type\":\"uint256\",\"name\":\"blockNumber\"},{\"type\":\"uint256\",\"name\":\"uptime\"},{\"type\":\"uint256\",\"name\":\"kernelScore\"}]}]},{\"type\":\"function\",\"name\":\"getMetadata\",\"constant\":true,\"stateMutability\":\"pure\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"string\",\"name\":\"contractName\"},{\"type\":\"string\",\"name\":\"contractVersion\"},{\"type\":\"string\",\"name\":\"contractDescription\"}]},{\"type\":\"function\",\"name\":\"isHealthy\",\"constant\":true,\"stateMutability\":\"pure\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"bool\",\"name\":\"healthy\"}]},{\"type\":\"function\",\"name\":\"name\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"string\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"owner\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"address\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"protectedHeartbeat\",\"constant\":false,\"payable\":false,\"inputs\":[{\"type\":\"tuple\",\"name\":\"krnlPayload\",\"components\":[{\"type\":\"bytes\",\"name\":\"auth\"},{\"type\":\"bytes\",\"name\":\"kernelResponses\"},{\"type\":\"bytes\",\"name\":\"kernelParams\"}]},{\"type\":\"string\",\"name\":\"input\"}],\"outputs\":[]},{\"type\":\"function\",\"name\":\"renounceOwnership\",\"constant\":false,\"payable\":false,\"inputs\":[],\"outputs\":[]},{\"type\":\"function\",\"name\":\"setTokenAuthorityPublicKey\",\"constant\":false,\"payable\":false,\"inputs\":[{\"type\":\"address\",\"name\":\"_tokenAuthorityPublicKey\"}],\"outputs\":[]},{\"type\":\"function\",\"name\":\"tokenAuthorityPublicKey\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"address\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"transferOwnership\",\"constant\":false,\"payable\":false,\"inputs\":[{\"type\":\"address\",\"name\":\"newOwner\"}],\"outputs\":[]},{\"type\":\"function\",\"name\":\"version\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"string\",\"name\":\"\"}]}]", "bytecode": "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", "metadata": {"name": "heartbeat.kernel", "version": "1.0.0", "description": "A KRNL-integrated kernel that returns health status and current block timestamp for on-chain monitoring"}}